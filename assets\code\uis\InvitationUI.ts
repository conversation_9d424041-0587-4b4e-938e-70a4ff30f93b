const { ccclass, property } = cc._decorator;
import UIbase from '../utils/UIbase';
import PrefabUtil from '../utils/manager/PrefabUtil';
import AudioMgr from '../manager/AudioMgr';
import AudioPath from '../datas/AudioPath';
import LocalData from '../manager/LocalData';
import InvitationCodes from '../datas/InvitationCodes';

@ccclass
export default class InvitationUI extends UIbase {

    @property(cc.EditBox)
    input: cc.EditBox = null; // 邀请码输入框

    @property(cc.Button)
    checkmark: cc.Button = null; // 确认按钮

    @property(cc.Button)
    btn_close: cc.Button = null; // 关闭按钮

    @property(cc.Label)
    messageLabel: cc.Label = null; // 消息显示标签

    @property(cc.Node)
    panel: cc.Node = null; // 弹窗主体

    private static _inst: InvitationUI;

    public static get inst() {
        console.log("=== InvitationUI.inst 被调用 ===");

        if (this._inst == null || this._inst.node == null) {
            console.log("需要创建新的InvitationUI实例");

            let prefab = PrefabUtil.get("InvitationUI");
            console.log("获取到的预制体:", prefab);

            if (!prefab) {
                console.error("❌ InvitationUI预制体未找到");
                return null;
            }

            console.log("开始实例化预制体");
            let v = cc.instantiate(prefab);
            console.log("预制体实例化完成，节点:", v);

            console.log("获取InvitationUI组件");
            this._inst = v.getComponent(InvitationUI);
            console.log("组件获取结果:", this._inst);

            if (!this._inst) {
                console.error("❌ 无法获取InvitationUI组件，请检查预制体是否正确添加了脚本组件");
                return null;
            }
        }

        console.log("返回InvitationUI实例:", this._inst);
        return this._inst;
    }

    start() {
        this.initUI();
    }

    private initUI() {
        // 初始化输入框
        if (this.input) {
            this.input.string = "";
            this.input.placeholder = "请输入6位邀请码";
            this.input.maxLength = 6;

            // 监听输入框回车事件
            this.input.node.on('editing-return', this.onInputReturn, this);
            // 监听输入框开始编辑事件
            this.input.node.on('editing-did-began', this.onInputDidBegan, this);
        }

        // 初始化消息标签
        if (this.messageLabel) {
            this.messageLabel.string = "";
            this.messageLabel.node.active = false;
        }

        // 绑定按钮事件
        if (this.checkmark) {
            this.checkmark.node.on(cc.Node.EventType.TOUCH_END, this.onClickCheckmark, this);
        }

        if (this.btn_close) {
            this.btn_close.node.on(cc.Node.EventType.TOUCH_END, this.onClickClose, this);
        }
    }

    protected onShow(): void {
        // 重置输入框工作状态
        this.inputBoxWorking = false;

        // 手机端和PC端不同的显示逻辑
        if (cc.sys.isMobile) {
            // 手机端：将输入框改为可点击的按钮样式
            if (this.input) {
                this.input.node.active = true;
                this.input.string = "点击输入邀请码";
                this.input.placeholder = "点击输入邀请码";
                // 禁用输入框的编辑功能，只作为点击区域
                this.input.enabled = false;
                // 添加点击事件
                this.input.node.off(cc.Node.EventType.TOUCH_END);
                this.input.node.on(cc.Node.EventType.TOUCH_END, this.onMobileInputClick, this);
            }

            if (this.messageLabel) {
                this.messageLabel.string = "";
                this.messageLabel.node.active = false;
            }
        } else {
            // PC端：正常显示输入框
            if (this.input) {
                this.input.node.active = true;
                this.input.enabled = true;
                this.input.string = "";
                this.input.placeholder = "请输入6位邀请码";
                // 移除手机端的点击事件
                this.input.node.off(cc.Node.EventType.TOUCH_END);
            }

            if (this.messageLabel) {
                this.messageLabel.string = "";
                this.messageLabel.node.active = false;
            }
        }

        // 播放弹窗动画
        this.playShowAnimation();
    }

    protected onHide(): void {
        // 隐藏时清理
        this.hideMessage();
    }

    /**
     * 手机端输入框点击事件
     */
    private onMobileInputClick() {
        AudioMgr.playSound(AudioPath.CLICK);
        this.showNativeInputDialog();
    }

    /**
     * 输入框开始编辑事件
     */
    private onInputDidBegan() {
        this.inputBoxWorking = true;
    }

    private inputBoxWorking: boolean = false;

    /**
     * 显示原生输入对话框（手机端专用）
     */
    private showNativeInputDialog() {
        const code = prompt("请输入6位邀请码:");

        if (!code) {
            return;
        }

        const trimmedCode = code.trim();

        if (trimmedCode.length !== 6) {
            alert("请输入6位邀请码");
            this.scheduleOnce(() => {
                this.showNativeInputDialog();
            }, 0.1);
            return;
        }

        // 更新输入框显示
        if (this.input) {
            this.input.string = trimmedCode;
        }

        // 验证邀请码
        this.validateInvitationCodeDirect(trimmedCode);
    }

    /**
     * 输入框回车事件处理
     */
    private onInputReturn() {
        this.validateInvitationCode();
    }

    /**
     * 确认按钮点击事件
     */
    public onClickCheckmark() {
        AudioMgr.playSound(AudioPath.CLICK);

        // 手机端和PC端都使用输入框验证
        if (cc.sys.isMobile) {
            // 手机端：如果输入框显示的是提示文字，弹出输入对话框
            if (this.input && this.input.string === "点击输入邀请码") {
                this.showNativeInputDialog();
            } else {
                this.validateInvitationCode();
            }
        } else {
            this.validateInvitationCode();
        }
    }

    /**
     * 关闭按钮点击事件
     */
    public onClickClose() {
        AudioMgr.playSound(AudioPath.CLICK);
        this.hideUI();
    }

    /**
     * 验证邀请码
     */
    private validateInvitationCode() {
        if (!this.input) {
            console.error("输入框未绑定");
            return;
        }

        const code = this.input.string.trim();
        this.validateInvitationCodeDirect(code);
    }

    /**
     * 直接验证邀请码（支持外部传入代码）
     */
    private validateInvitationCodeDirect(code: string) {
        // 检查输入长度
        if (code.length !== 6) {
            if (cc.sys.isMobile) {
                alert("请输入6位邀请码");
                this.scheduleOnce(() => {
                    this.showNativeInputDialog();
                }, 0.1);
            } else {
                this.showMessage("请输入6位邀请码", false);
            }
            return;
        }

        // 验证邀请码
        if (InvitationCodes.validateCode(code)) {
            this.onInvitationSuccess(code);
        } else {
            if (cc.sys.isMobile) {
                alert("邀请码错误，请重新输入");
                this.scheduleOnce(() => {
                    this.showNativeInputDialog();
                }, 0.1);
            } else {
                this.showMessage("邀请码错误", false);
            }
        }
    }

    /**
     * 邀请码验证成功处理
     */
    private onInvitationSuccess(code: string) {
        // 设置VIP状态
        LocalData.isVipUser = true;
        LocalData.usedRedeemCode = code;

        // 显示成功消息
        if (cc.sys.isMobile) {
            alert("兑换码使用成功！VIP权限已激活");
            this.scheduleOnce(() => {
                this.hideUI();
            }, 0.5);
        } else {
            this.showMessage("兑换码使用成功！VIP权限已激活", true);
            this.scheduleOnce(() => {
                this.hideUI();
            }, 2);
        }

        // 发送VIP状态变化事件
        cc.systemEvent.emit('vipStatusChanged', true);
    }

    /**
     * 显示消息
     */
    private showMessage(message: string, isSuccess: boolean) {
        if (!this.messageLabel) {
            console.error("消息标签未绑定");
            return;
        }

        this.messageLabel.string = message;
        this.messageLabel.node.active = true;
        
        // 设置消息颜色
        if (isSuccess) {
            this.messageLabel.node.color = cc.Color.GREEN;
        } else {
            this.messageLabel.node.color = cc.Color.RED;
        }

        // 播放消息动画
        this.playMessageAnimation();
    }

    /**
     * 隐藏消息
     */
    private hideMessage() {
        if (this.messageLabel) {
            this.messageLabel.node.active = false;
        }
    }

    /**
     * 播放显示动画
     */
    private playShowAnimation() {
        console.log("=== 播放显示动画 ===");

        if (!this.panel) {
            console.warn("⚠️ panel节点未绑定，无法播放动画");
            return;
        }

        console.log("panel节点存在，开始动画");
        console.log("panel初始状态 - 位置:", this.panel.position, "缩放:", this.panel.scale);

        // 初始状态：缩放为0
        this.panel.scale = 0;

        // 缩放动画
        cc.tween(this.panel)
            .to(0.3, { scale: 1 }, { easing: 'backOut' })
            .call(() => {
                console.log("✅ 弹窗动画播放完成");
            })
            .start();
    }

    /**
     * 播放消息动画
     */
    private playMessageAnimation() {
        if (!this.messageLabel) return;

        // 淡入动画
        this.messageLabel.node.opacity = 0;
        cc.tween(this.messageLabel.node)
            .to(0.2, { opacity: 255 })
            .start();
    }

    /**
     * 销毁时清理事件监听
     */
    onDestroy() {
        if (this.input) {
            this.input.node.off('editing-return', this.onInputReturn, this);
        }
        if (this.checkmark) {
            this.checkmark.node.off(cc.Node.EventType.TOUCH_END, this.onClickCheckmark, this);
        }
        if (this.btn_close) {
            this.btn_close.node.off(cc.Node.EventType.TOUCH_END, this.onClickClose, this);
        }
    }
}
